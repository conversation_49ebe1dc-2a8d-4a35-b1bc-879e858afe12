import React, { useMemo, useRef, useState, useCallback } from 'react';
import { Box, TextField, Typography } from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { RowAction } from './RowAction';
import { extractGUID, formatDate } from '@/utils/commonUtility';
import strings from '@/utils/localization';
import { Close } from '@mui/icons-material';

const getDataGridColumns = (
  organizationId,
  widgetBaseUrl,
  handleEditWidgetCallback,
  handlePreviewWidgetCallback,
  handleDuplicateWidgetCallback,
  handleDeleteWidgetCallback,
  handleExportWidgetCallback,
) => [
  {
    field: 'name',
    headerName: 'Name',
    minWidth: 300,
    flex: 1,
    sortable: true,
    hideable: false,
    renderCell: (params) => {
      const { row } = params;
      return row.name;
    },
  },
  {
    field: 'updatedAt',
    headerName: 'Modified',
    width: 150,
    sortable: true,
    renderCell: (params) => formatDate(params.value, 'yyyy-MM-dd'),
  },
  {
    field: 'createdAt',
    headerName: 'Created',
    width: 150,
    sortable: true,
    renderCell: (params) => formatDate(params.value, 'yyyy-MM-dd'),
  },
  {
    field: 'type',
    headerName: 'Type',
    width: 200,
    sortable: true,
    disableColumnMenu: true,
  },
  {
    field: 'Action',
    headerName: '',
    width: 80,
    sortable: false,
    disableColumnMenu: true,
    hideable: false,
    renderCell: (params) => (
      <RowAction
        organizationId={organizationId}
        widgetBaseUrl={widgetBaseUrl}
        widgetData={params.row.widgetData}
        handleEditWidgetCallback={handleEditWidgetCallback}
        handlePreviewWidgetCallback={handlePreviewWidgetCallback}
        handleDuplicateWidgetCallback={handleDuplicateWidgetCallback}
        handleDeleteWidgetCallback={handleDeleteWidgetCallback}
        handleExportWidgetCallback={handleExportWidgetCallback}
      />
    ),
  },
];

export const WidgetList = (props) => {
  const {
    organizationId,
    widgetBaseUrl,
    widgetsList,
    handleEditWidgetCallback,
    handlePreviewWidgetCallback,
    handleDuplicateWidgetCallback,
    handleDeleteWidgetCallback,
    activeVerificationWidgetId,
    handleExportWidgetCallback,
  } = props;

  // Get DataGrid settings from localStorage
  const getDataGridStateFromStorage = () => {
    try {
      const storedState = localStorage.getItem('widgetDataGridState');
      return storedState ? JSON.parse(storedState) : {};
    } catch {
      return {};
    }
  };

  const searchFieldRef = useRef(null);
  const [searchText, setSearchText] = useState('');
  const [dataGridState, setDataGridState] = useState(getDataGridStateFromStorage());

  // Save DataGrid state to localStorage
  const saveDataGridState = useCallback((newState) => {
    setDataGridState(newState);
    localStorage.setItem('widgetDataGridState', JSON.stringify(newState));
  }, []);

  const rows = useMemo(() => {
    if (!widgetsList?.length) return [];

    return widgetsList.map((widget) => ({
      id: extractGUID(widget?.SK),
      name: widget?.name,
      updatedAt: widget?.updatedAt,
      createdAt: widget?.createdAt,
      type: widget?.widgetType,
      widgetData: widget, // Store original widget data for RowAction
    }));
  }, [widgetsList]);

  const filteredRows = useMemo(() => {
    if (!searchText) return rows;

    return rows.filter(
      (row) =>
        row.name.toLowerCase().includes(searchText.toLowerCase()) ||
        row.type.toLowerCase().includes(searchText.toLowerCase()),
    );
  }, [rows, searchText]);

  const handleWidgetsSearch = useCallback((e) => {
    const searchString = e?.target?.value || '';
    setSearchText(searchString);
  }, []);

  const handleClearSearchKey = useCallback(() => {
    setSearchText('');
    if (searchFieldRef.current) {
      searchFieldRef.current.value = '';
    }
  }, []);

  const columns = useMemo(
    () =>
      getDataGridColumns(
        organizationId,
        widgetBaseUrl,
        handleEditWidgetCallback,
        handlePreviewWidgetCallback,
        handleDuplicateWidgetCallback,
        handleDeleteWidgetCallback,
        handleExportWidgetCallback,
        activeVerificationWidgetId,
      ),
    [
      organizationId,
      widgetBaseUrl,
      handleEditWidgetCallback,
      handlePreviewWidgetCallback,
      handleDuplicateWidgetCallback,
      handleDeleteWidgetCallback,
      handleExportWidgetCallback,
      activeVerificationWidgetId,
    ],
  );

  return (
    <Box>
      <Box sx={{ mb: 2 }}>
        <TextField
          autoComplete="off"
          size="small"
          onChange={handleWidgetsSearch}
          placeholder={strings.search}
          inputRef={searchFieldRef}
          sx={{ width: { md: 250 }, maxWidth: '100%' }}
          InputProps={{
            endAdornment: searchText && (
              <Close onClick={handleClearSearchKey} sx={{ color: 'grey', cursor: 'pointer' }} />
            ),
          }}
        />
      </Box>
      {widgetsList?.length && filteredRows?.length ? (
        <Box sx={{ width: '100%' }}>
          <DataGrid
            rows={filteredRows}
            columns={columns}
            initialState={{
              sorting: {
                sortModel: dataGridState.sortModel || [{ field: 'name', sort: 'asc' }],
              },
              columns: {
                columnVisibilityModel: dataGridState.columnVisibilityModel || {
                  createdAt: false,
                },
              },
              pagination: {
                paginationModel: { pageSize: 15 },
              },
            }}
            onSortModelChange={(sortModel) => {
              saveDataGridState({
                ...dataGridState,
                sortModel,
              });
            }}
            onColumnVisibilityModelChange={(columnVisibilityModel) => {
              saveDataGridState({
                ...dataGridState,
                columnVisibilityModel,
              });
            }}
            disableRowSelectionOnClick
            disableColumnFilter
            disableDensitySelector
            disableCellFocus
            sortingOrder={['asc', 'desc']}
            pageSizeOptions={[10, 15, 25, 50]}
            paginationMode="client"
            disableColumnResize={true}
            sx={{
              borderRadius: 1,
              '& .MuiDataGrid-cell, & .MuiDataGrid-columnHeader': {
                py: '0.6rem',
                px: 2,
                outline: 'none !important',
                display: 'flex',
                alignItems: 'center',
              },
              '& .MuiDataGrid-cell': {
                fontSize: '0.9rem',
              },
              '& .MuiDataGrid-columnHeader': {
                fontSize: '0.9rem',
                fontWeight: 700,
              },
            }}
          />
        </Box>
      ) : (
        <Typography>No Widgets found</Typography>
      )}
    </Box>
  );
};
