import React, { useEffect, useState, useMemo, useRef } from 'react';

const EMPTY_PAGE_CONFIG = { heading: '', description: '' };
import { Box, Button, Grid, Paper, Stack, Tab, Tabs, Typography, Select, MenuItem, FormControl } from '@mui/material';
import { HeaderWithActions, PanelBorder } from '@/components';
import { useFieldArray, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { actionFieldsData } from './data/actionFieldsData';
import { fieldsValidation, schema } from './data/schema';
import { extractGUID, generateFieldsList } from '@/utils/commonUtility';
import { BOOKING_CAPS } from '@/utils/constants';
import { Settings } from '@/components/Settings';
import { ClientInformation } from '@/components/ClientInformation';
import { Internationalization } from '@/components/Internationalization';
import { Actions } from './components/Actions';
import { BookingSettings } from './BookingSettings';
import { BookingWorkflow } from './components/BookingWorkflow';
import strings from '@/utils/localization';
import { Language } from '@/components/Language';

export const AddNewBookingWidget = (props) => {
  const {
    widgetsList,
    handleNavigationCallback,
    existingWidgetData,
    handleSaveOrUpdateWidgetCallback,
    bookingWidgetFields,
    locationsList,
    servicesList,
    allIdTypes,
    orgRequiredIdTypes,
    fetchWidgetWithLanguage,
  } = props;

  const {
    SK,
    PK,
    createdAt,
    otpVerificationEnabled,
    clientInformationPageTitle,
    clientInformationPageSubtitle,
    fields: availableFields = [],
    enableMap,
    mapPlaceholderImage,
    introduction,
    locations,
    multipleIndividualEnabled,
    matchingInfoOnly,
    name,
    showClientInformation,
    action,
    services,
    showSignIn,
    widgetTitle,
    defaultLanguage,
    currentLanguage,
    bookingSummaryDescription,
    isConsentRequired,
    individualNotFoundPage,
    locationRequired,
    selfRegistration,
    byRequestOnly,
    requestNotFoundPage,
    appointmentNotFoundPage,
    preConfirmationMessage,
    confirmationMessage,
    cancellationMessage,
    confirmationPage,
  } = existingWidgetData || {};

  const allowedIdentifications = [{ label: strings.identified, value: 'IDENTIFIED' }];

  const bookingWidgetGUID = extractGUID(SK);
  const [selectedServices, setSelectedServices] = useState(services ? services : []);
  const [enableLogin, setEnableLogin] = useState(showSignIn !== undefined ? showSignIn : true);
  const [selectedIdTypes, setSelectedIdTypes] = useState(() => {
    const healthCareField =
      availableFields?.find((field) => field?.code === 'IDENTIFICATION') ||
      bookingWidgetFields?.find((field) => field?.code === 'IDENTIFICATION');
    return healthCareField?.idTypes || [];
  });

  const [fieldsList, setFieldsList] = useState(() =>
    generateFieldsList(bookingWidgetFields, availableFields, selectedIdTypes),
  );
  const [selectedFields, setSelectedFields] = useState(fieldsList.filter((field) => field.systemRequired));

  const [selectedTab, setSelectedTab] = useState(0);
  const [fieldsValidationError, setFieldsValidationError] = useState('');
  const [isDemographicsEnabled, setIsDemographicsEnabled] = useState(
    showClientInformation === undefined ? true : showClientInformation,
  );
  const [languageChanged, setLanguageChanged] = useState(false);
  const initialSelectedFieldsRef = useRef(structuredClone(fieldsList.filter((field) => field.checked)));
  const handleWidgetLanguageChangeRef = useRef(null);

  const mapPlaceholderImageDetails = mapPlaceholderImage?.filename?.split(';') || [];
  const fetchImageAsBase64 = async (url) => {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          const base64 = reader.result.split(',')[1];
          resolve(base64);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      console.error('Error fetching image:', error);
      return '';
    }
  };

  const mapImagePlaceholderInitialValue = {
    filename: mapPlaceholderImageDetails[0] || '',
    type: mapPlaceholderImageDetails[1] || '',
    size: mapPlaceholderImageDetails[2] || '',
    imageBase64: mapPlaceholderImage?.base64 || '',
    imagePresignedUrl: mapPlaceholderImage?.imagePresignedUrl || '',
  };

  useEffect(() => {
    if (mapPlaceholderImage?.imagePresignedUrl && !mapPlaceholderImage?.base64) {
      (async () => {
        const base64 = await fetchImageAsBase64(mapPlaceholderImage.imagePresignedUrl);
        if (base64) {
          setValue('mapPlaceholderImage', {
            ...mapImagePlaceholderInitialValue,
            imageBase64: base64,
          });
        }
      })();
    }
  }, [mapPlaceholderImage?.imagePresignedUrl]);

  const defaultValues = {
    name: name || '',
    widgetTitle: widgetTitle,
    services: services || [],
    locations: locations || [],
    isConsentRequired: isConsentRequired !== undefined ? isConsentRequired : true,
    enableLogin: showSignIn === undefined ? true : showSignIn,
    isOtpVerificationChecked: otpVerificationEnabled || false,
    mapEnabled: enableMap === undefined ? true : enableMap,
    locationRequired: locationRequired === undefined ? false : locationRequired,
    mapPlaceholderImage: mapImagePlaceholderInitialValue,
    defaultLanguage: defaultLanguage !== undefined ? defaultLanguage : 'en',
    currentLanguage: currentLanguage !== undefined ? currentLanguage : 'en',
    isMultipleIndividualChecked: multipleIndividualEnabled || false,
    matchingInfoOnly: matchingInfoOnly === undefined ? true : matchingInfoOnly,
    isIntroChecked: introduction?.enabled === undefined ? false : introduction?.enabled,
    introHeading: introduction?.enabled ? introduction?.heading : '',
    introDescription: introduction?.enabled ? introduction?.description : '',
    introButton: introduction?.enabled ? introduction?.buttonText : '',
    isConfirmationChecked: confirmationPage?.enabled === undefined ? true : confirmationPage?.enabled,
    confirmationHeading: confirmationPage?.enabled ? confirmationPage?.heading : '',
    confirmationDescription: confirmationPage?.enabled ? confirmationPage?.description : '',
    confirmationButton: confirmationPage?.enabled ? confirmationPage?.buttonText : '',
    isDemographicsEnabled: showClientInformation !== undefined ? showClientInformation : true,
    isActionEnabled: action?.enabled === undefined ? false : action?.enabled,
    actionData: action?.enabled ? action?.actionConditions : [actionFieldsData],
    actionButtonText: action?.actionButton || '',
    bookingSummaryDescription: bookingSummaryDescription || '',
    byRequestOnly: byRequestOnly || false,
    requestNotFoundPage: requestNotFoundPage || EMPTY_PAGE_CONFIG,
    appointmentNotFoundPage: appointmentNotFoundPage || EMPTY_PAGE_CONFIG,
    individualNotFoundPage: individualNotFoundPage || EMPTY_PAGE_CONFIG,
    fields: selectedFields,
    clientInformationPageTitle: clientInformationPageTitle || '',
    clientInformationPageSubtitle: clientInformationPageSubtitle || '',
    identification: 'IDENTIFIED',
    selfRegister: selfRegistration === undefined ? false : selfRegistration,
    preConfirmationMessage: preConfirmationMessage || '',
    confirmationMessage: confirmationMessage || '',
    cancellationMessage: cancellationMessage || '',
  };

  const {
    register,
    watch,
    control,
    handleSubmit,
    reset,
    setValue,
    getValues,
    trigger,
    formState: { isDirty, errors },
  } = useForm({
    mode: 'onBlur',
    resolver: yupResolver(schema),
    defaultValues,
    shouldUnregister: false,
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'actionData',
  });

  const addField = () => {
    append({ ...actionFieldsData, default: false });
  };

  const removeField = (index) => {
    remove(index);
  };

  const formData = {
    register,
    errors,
    control,
    reset,
    actionData: fields,
    addField,
    removeField,
    setValue,
    getValues,
    trigger,
    watch,
  };

  useEffect(() => {
    if (existingWidgetData) {
      reset(defaultValues, { keepDefaultValues: false });
      setSelectedServices(services);
    }
  }, [existingWidgetData]);

  const validateFields = async (selectedFields) => {
    try {
      await fieldsValidation.validate(selectedFields);
      setFieldsValidationError('');
    } catch (error) {
      setFieldsValidationError(error.errors[0]);
      throw new Error(error.errors[0]);
    }
  };

  const isFieldsChanged = useMemo(() => {
    const currentInitialFields = initialSelectedFieldsRef.current;
    if (!currentInitialFields || selectedFields.length !== currentInitialFields.length) return true;

    return selectedFields.some((field) => {
      const initialField = currentInitialFields.find((f) => f.code === field.code);
      if (!initialField) return true;

      return (
        field.checked !== initialField.checked ||
        field.isMandatory !== initialField.isMandatory ||
        field.display !== initialField.display ||
        field.allowMultiple !== initialField.allowMultiple ||
        field.position !== initialField.position ||
        (field.code === 'IDENTIFICATION' &&
          JSON.stringify(field.idTypes?.sort()) !== JSON.stringify(initialField.idTypes?.sort()))
      );
    });
  }, [selectedFields, initialSelectedFieldsRef.current]);
  const isFormDirty = useMemo(
    () => isDirty || isFieldsChanged || languageChanged,
    [isDirty, isFieldsChanged, languageChanged],
  );

  // Debug: Log when form becomes dirty
  useEffect(() => {
    if (isDirty) {
      console.log('Form is dirty - isDirty:', isDirty);
      console.log('Current form values:', getValues());
    }
  }, [isDirty, getValues]);

  const nameValue = watch('name', strings.untitledBookingWidget || 'Untitled Booking Widget');
  const handleSave = async (formData) => {
    await validateFields(selectedFields);

    const mapPlaceholderImage = formData?.mapPlaceholderImage;
    if (mapPlaceholderImage?.imagePresignedUrl && !mapPlaceholderImage?.imageBase64) {
      try {
        const base64 = await fetchImageAsBase64(mapPlaceholderImage.imagePresignedUrl);
        if (base64) {
          mapPlaceholderImage.imageBase64 = base64;
        }
      } catch (error) {
        console.error('Error converting image to base64:', error);
      }
    }

    const updatedFields = selectedFields.map((field) => {
      if (field.code === 'IDENTIFICATION') {
        return { ...field, idTypes: selectedIdTypes };
      }
      return field;
    });

    let bookingData = {
      name: formData.name,
      widgetTitle: formData.widgetTitle,
      showSignIn: enableLogin,
      otpVerificationEnabled: formData.isOtpVerificationChecked,
      clientInformationPageTitle: formData.clientInformationPageTitle || '',
      clientInformationPageSubtitle: formData.clientInformationPageSubtitle || '',
      fields: updatedFields,
      enableMap: formData?.mapEnabled || false,
      locationRequired: formData?.locationRequired,
      isConsentRequired: formData?.isConsentRequired,
      mapPlaceholderImage: {
        filename:
          mapPlaceholderImage?.filename && mapPlaceholderImage?.type && mapPlaceholderImage?.size
            ? `${mapPlaceholderImage?.filename || ''};${mapPlaceholderImage?.type || ''};${
                mapPlaceholderImage?.size || ''
              }`
            : '',
        base64: mapPlaceholderImage?.imageBase64 || '',
      },
      services: formData.services,
      locations: formData.locations,
      defaultLanguage: formData.defaultLanguage,
      currentLanguage: formData.currentLanguage,
      showClientInformation: formData.isDemographicsEnabled,
      introduction: {
        enabled: formData.isIntroChecked,
        heading: formData.introHeading || '',
        description: formData.introDescription || '',
        buttonText: formData.introButton || '',
      },
      confirmationPage: {
        enabled: formData.isConfirmationChecked,
        heading: formData.confirmationHeading || '',
        description: formData.confirmationDescription || '',
        buttonText: formData.confirmationButton || '',
      },
      action: {
        enabled: formData.isActionEnabled,
        actionButton: formData?.actionButtonText,
        actionConditions: formData.actionData,
      },
      individualNotFoundPage: formData.individualNotFoundPage,
      multipleIndividualEnabled: formData.isMultipleIndividualChecked,
      bookingSummaryDescription: formData.bookingSummaryDescription,
      identification: 'IDENTIFIED',
      matchingInfoOnly: formData.matchingInfoOnly,
      selfRegistration: formData.selfRegister,
      preConfirmationMessage: formData.preConfirmationMessage,
      confirmationMessage: formData.confirmationMessage,
      cancellationMessage: formData.cancellationMessage,
      byRequestOnly: formData.byRequestOnly,
      requestNotFoundPage: formData.requestNotFoundPage,
      appointmentNotFoundPage: formData.appointmentNotFoundPage,
    };

    if (SK && PK) {
      bookingData.SK = SK;
      bookingData.PK = PK;
      bookingData.createdAt = createdAt || new Date().toISOString();
    }

    const result = await handleSaveOrUpdateWidgetCallback(bookingData, BOOKING_CAPS);
    console.log('booking widget result', result);
    if (result.success) {
      // reset(formData);
      setLanguageChanged(false);
    }
  };

  const onValid = async (formData) => {
    await handleSave(formData);
    initialSelectedFieldsRef.current = structuredClone(selectedFields);
  };

  const onError = (errors) => {
    const getErrorElement = (error) => {
      const errorRefName = error?.ref?.name;
      return errorRefName ? document.querySelector(`[name="${errorRefName}"]`) : null;
    };
    for (const key in errors) {
      if (key !== 'actionData') {
        const generalError = getErrorElement(errors[key]);
        if (generalError) {
          generalError.scrollIntoView({ behavior: 'smooth', block: 'center' });
          generalError.focus();
          return;
        }
      }
    }
    if (errors.actionData && Array.isArray(errors.actionData)) {
      for (const actionErrorFields of errors.actionData) {
        for (const fieldKey in actionErrorFields) {
          const actionError = getErrorElement(actionErrorFields[fieldKey]);
          if (actionError) {
            actionError.scrollIntoView({ behavior: 'smooth', block: 'center' });
            actionError.focus();
            return;
          }
        }
      }
    }
  };

  const handleSelectServices = (updatedServices) => {
    setSelectedServices(updatedServices);
    //check actions data and update selected services for validation
    const actionData = getValues('actionData');
    if (actionData?.length) {
      const selectedServiceIds = new Set(updatedServices.map((service) => service.id));

      const updatedActionData = actionData.map((action) => {
        if (action.selectedServices?.length) {
          const updatedServices = action.selectedServices.filter((service) => selectedServiceIds.has(service.id));

          return {
            ...action,
            selectedServices: updatedServices,
          };
        }
        return action;
      });

      setValue('actionData', updatedActionData);
      updatedActionData.forEach((_, index) => {
        trigger(`actionData[${index}].selectedServices`);
      });
    }
  };

  const handleSelectFields = (fields) => {
    const updatedFields = fields
      .map((field) => {
        if (field.code === 'IDENTIFICATION') {
          return {
            ...field,
            idTypes: selectedIdTypes,
            checked: field.checked || (field.systemRequired && field.systemMandatory) || false,
            isMandatory: field.isMandatory || false,
          };
        }
        return {
          ...field,
          checked: field.checked || (field.systemRequired && field.systemMandatory) || false,
          isMandatory: field.isMandatory || false,
        };
      })
      .filter((field) => field.checked);
    setSelectedFields(updatedFields);
    validateFields(updatedFields);
  };
  useEffect(() => {
    handleSelectFields(selectedFields);
  }, [selectedIdTypes]);

  const handleTabChange = (_, newValue) => {
    setSelectedTab(newValue);
  };

  return (
    <>
      <Box sx={{ padding: '16px' }}>
        <HeaderWithActions
          title={nameValue || strings.untitledBookingWidget}
          actionButtons={[
            <Language
              key="language"
              SK={SK}
              PK={PK}
              widgetType={BOOKING_CAPS}
              setValue={setValue}
              reset={reset}
              strings={strings}
              fetchWidgetCallback={fetchWidgetWithLanguage}
              getValues={getValues}
              watch={watch}
              isFormDirty={isFormDirty}
              onSaveBeforeLanguageChange={() =>
                new Promise((resolve) => {
                  handleSubmit((formData) => {
                    onValid(formData).then(() => {
                      resolve();
                    });
                  }, onError)();
                })
              }
              setLanguageChanged={setLanguageChanged}
              onLanguageChange={(fn) => {
                handleWidgetLanguageChangeRef.current = fn;
              }}
            />,
            <Button key="save" variant="contained" onClick={handleSubmit(onValid, onError)} disabled={!isFormDirty}>
              {strings.save}
            </Button>,
          ]}
        />

        <PanelBorder>
          <Grid container>
            <Grid item xs={12} sm={12} paddingLeft={2} paddingRight={2}>
              {/* Tabs */}
              <Tabs value={selectedTab} onChange={handleTabChange}>
                <Tab label={strings.settings} disableRipple />
                <Tab label={strings.clientInformation} disableRipple />
                <Tab label={strings.workflow} disableRipple />
                <Tab label={strings.internationalization} disableRipple />
              </Tabs>

              <Paper elevation={3} sx={{ padding: '16px' }}>
                {selectedTab === 0 && ( //use object instead of numbers
                  <>
                    <Settings validationData={formData} setEnableLogin={setEnableLogin} />
                    <BookingSettings
                      existingWidgetData={existingWidgetData}
                      validationData={formData}
                      handleSelectServices={handleSelectServices}
                      availableLocations={locationsList}
                      availableServices={servicesList}
                    />
                  </>
                )}
                {selectedTab === 1 && (
                  <>
                    <ClientInformation
                      existingWidgetData={existingWidgetData}
                      widgetFields={bookingWidgetFields}
                      validationData={formData}
                      fieldsValidationError={fieldsValidationError}
                      handleSelectFields={handleSelectFields}
                      selectedIdTypes={selectedIdTypes}
                      setSelectedIdTypes={setSelectedIdTypes}
                      allIdTypes={allIdTypes}
                      orgRequiredIdTypes={orgRequiredIdTypes}
                      setIsDemographicsEnabled={setIsDemographicsEnabled}
                      isDemographicsEnabled={isDemographicsEnabled}
                      allowedIdentifications={allowedIdentifications}
                      fieldsList={fieldsList}
                      setFieldsList={setFieldsList}
                    />
                  </>
                )}
                {selectedTab === 2 && (
                  <>
                    <BookingWorkflow
                      existingWidgetData={existingWidgetData}
                      validationData={formData}
                      widgetsList={widgetsList}
                    />
                    <Actions
                      allAvailableWidgetsList={widgetsList}
                      existingWidgetData={existingWidgetData}
                      validationData={formData}
                      selectedServicesList={selectedServices}
                    />
                  </>
                )}
                {selectedTab === 3 && (
                  <Internationalization
                    existingWidgetData={existingWidgetData}
                    validationData={formData}
                    handleWidgetLanguageChange={handleWidgetLanguageChangeRef.current}
                  />
                )}
              </Paper>
            </Grid>
          </Grid>
        </PanelBorder>
      </Box>
    </>
  );
};
