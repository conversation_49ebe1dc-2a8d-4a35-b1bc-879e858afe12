export const getSampleTranslationStructure = (widgetType) => {
  // Base sample with common fields
  const commonSample = {
    widgetTitle: {
      en: 'Widget Title',
      fr: 'Titre du Widget',
    },
    clientInformationPageTitle: {
      en: 'Client Information',
      fr: 'Informations client',
    },
    clientInformationPageSubtitle: {
      en: 'Please provide your information',
      fr: 'Veuillez fournir vos informations',
    },
    introduction: {
      heading: {
        en: 'Welcome',
        fr: 'Bienvenue',
      },
      description: {
        en: 'Continue to proceed',
        fr: 'Continuez pour procéder',
      },
      buttonText: {
        en: 'Start',
        fr: 'Commencer',
      },
    },
    individualNotFoundPage: {
      heading: {
        en: 'Client not found',
        fr: 'Client non trouvé',
      },
      description: {
        en: 'Please contact us',
        fr: 'Veuillez nous contacter',
      },
    },
  };

  // Type-specific fields
  const bookingSample = {
    appointmentNotFoundPage: {
      heading: {
        en: 'Appointment Not Found',
        fr: 'Rendez-vous non trouvé',
      },
      description: {
        en: 'Please get in touch with us',
        fr: 'Veuillez nous contacter',
      },
    },
    confirmationMessage: {
      en: 'The appointment is confirmed',
      fr: 'Le rendez-vous est confirmé',
    },
    cancellationMessage: {
      en: 'The appointment has been cancelled',
      fr: 'Le rendez-vous a été annulé',
    },
    preConfirmationMessage: {
      en: 'A reminder will be sent to you before your appointment',
      fr: 'Un rappel vous sera envoyé avant votre rendez-vous',
    },
  };

  const questionnaireSample = {
    nextButtonText: {
      en: 'Next',
      fr: 'Suivant',
    },
    previousButtonText: {
      en: 'Previous',
      fr: 'Précédent',
    },
    doneButtonText: {
      en: 'Done',
      fr: 'Terminé',
    },
  };

  const registrationSample = {
    finalPage: {
      heading: {
        en: 'Registration Complete',
        fr: 'Inscription Terminée',
      },
      description: {
        en: 'Thank you for registering',
        fr: 'Merci de vous être inscrit',
      },
    },
  };

  // Return appropriate sample based on widget type
  switch (widgetType?.toLowerCase()) {
    case 'booking':
      return { ...commonSample, ...bookingSample };
    case 'questionnaire':
      return { ...commonSample, ...questionnaireSample };
    case 'registration':
      return { ...commonSample, ...registrationSample };
    default:
      return commonSample;
  }
};

// Default export
export default {
  getSampleTranslationStructure,
};
